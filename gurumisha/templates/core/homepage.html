{% extends 'base.html' %}
{% load static %}
{% load math_filters %}

{% block title %}Gurumisha - Premium Automotive Marketplace{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/homepage-enhancements.css' %}">
<link rel="stylesheet" href="{% static 'css/global-animations.css' %}">
<style>
    /* Admin-style components for homepage */
    .admin-request-add-btn {
        background: linear-gradient(135deg, #DC2626 0%, #B91C1C 25%, #991B1B 50%, #7F1D1D 75%, #1F2937 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        font-family: 'Montserrat', sans-serif;
        box-shadow: 0 8px 24px rgba(220, 38, 38, 0.4), 0 4px 12px rgba(31, 41, 55, 0.3);
    }

    .admin-request-add-btn:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 12px 32px rgba(220, 38, 38, 0.5), 0 6px 16px rgba(31, 41, 55, 0.4);
        color: white;
        text-decoration: none;
    }

    .admin-request-add-btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    .admin-stat-card {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        transition: all 0.2s ease-in-out;
    }

    .admin-stat-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
    }

    .admin-stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .admin-stat-label {
        font-size: 0.875rem;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }

    /* Enhanced glassmorphism effects */
    .glassmorphism-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Enhanced animations */
    .animate-entrance {
        animation: fadeInUp 0.8s ease-out both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Harrier color variables */
    :root {
        --harrier-red: #DC2626;
        --harrier-dark: #1F2937;
        --harrier-blue: #1E40AF;
        --harrier-gray: #F8FAFC;
    }
</style>
{% endblock %}

{% block content %}
<!-- Enhanced Hero Section with Video Background -->
<section class="main-banner">
    <video autoplay muted loop id="bg-video">
        <source src="{% static 'images/video.mp4' %}" type="video/mp4" />
        Your browser does not support the video tag.
    </video>

    <div class="video-overlay"></div>

    <div class="hero-content">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-screen py-20">
                <!-- Hero Text Content -->
                <div class="text-white space-y-8">
                    <div class="space-y-6">
                        <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight animate-fade-in-up font-montserrat">
                            Find Your
                            <span class="text-gradient bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
                                Perfect
                            </span>
                            Vehicle
                        </h1>
                        <p class="text-xl md:text-2xl text-gray-200 leading-relaxed max-w-2xl animate-fade-in-up animate-delay-300 font-inter">
                            Discover premium vehicles from trusted dealers. Buy, sell, or import with confidence in Kenya's leading automotive marketplace.
                        </p>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-6 animate-fade-in-up animate-delay-600">
                        <a href="{% url 'core:car_list' %}"
                           class="admin-request-add-btn group inline-flex items-center justify-center px-10 py-4 text-lg font-bold text-white rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-2xl font-montserrat relative overflow-hidden"
                           style="background: linear-gradient(135deg, #DC2626 0%, #B91C1C 25%, #991B1B 50%, #7F1D1D 75%, #1F2937 100%); box-shadow: 0 8px 24px rgba(220, 38, 38, 0.4), 0 4px 12px rgba(31, 41, 55, 0.3);">
                            <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <svg class="w-6 h-6 mr-3 transition-transform duration-300 group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <span class="relative z-10">Browse Cars</span>
                            <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-red-400 to-gray-800 opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
                        </a>
                        <a href="{% url 'core:sell_car' %}"
                           class="group inline-flex items-center justify-center px-10 py-4 text-lg font-bold text-white border-3 border-white/80 rounded-2xl transition-all duration-500 hover:bg-white hover:text-gray-900 font-montserrat relative overflow-hidden backdrop-blur-sm bg-white/10"
                           style="box-shadow: 0 10px 30px -5px rgba(255, 255, 255, 0.3);">
                            <div class="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <svg class="w-6 h-6 mr-3 transition-transform duration-300 group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span class="relative z-10">Sell Your Car</span>
                            <div class="absolute inset-0 rounded-2xl bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        </a>
                    </div>
                </div>

                <!-- Car Search Form -->
                <div class="car-search-form bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl">
                    <h3 class="text-2xl font-bold text-gray-900 mb-8 text-center">Find Your Perfect Car</h3>
                    <form id="car-search-form" action="{% url 'core:car_list' %}" method="GET">
                        <div class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="condition" class="block text-sm font-semibold text-gray-700 mb-2">Condition</label>
                                    <select name="condition" id="condition" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200">
                                        <option value="">Any Condition</option>
                                        <option value="new">New</option>
                                        <option value="used">Used</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="brand" class="block text-sm font-semibold text-gray-700 mb-2">Brand</label>
                                    <select name="brand" id="brand" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200">
                                        <option value="">Select Brand</option>
                                        {% for brand in car_brands %}
                                            <option value="{{ brand.id }}">{{ brand.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="vehicle_type" class="block text-sm font-semibold text-gray-700 mb-2">Vehicle Type</label>
                                    <select name="vehicle_type" id="vehicle_type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200">
                                        <option value="">Any Type</option>
                                        {% for type in vehicle_types %}
                                            <option value="{{ type|lower }}">{{ type }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="fuel_type" class="block text-sm font-semibold text-gray-700 mb-2">Fuel Type</label>
                                    <select name="fuel_type" id="fuel_type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200">
                                        <option value="">Any Fuel</option>
                                        <option value="petrol">Petrol</option>
                                        <option value="diesel">Diesel</option>
                                        <option value="hybrid">Hybrid</option>
                                        <option value="electric">Electric</option>
                                    </select>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="min_price" class="block text-sm font-semibold text-gray-700 mb-2">Min Price (KSh)</label>
                                    <select name="min_price" id="min_price" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200">
                                        <option value="">No Min</option>
                                        <option value="500000">500K</option>
                                        <option value="1000000">1M</option>
                                        <option value="2000000">2M</option>
                                        <option value="3000000">3M</option>
                                        <option value="5000000">5M</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="max_price" class="block text-sm font-semibold text-gray-700 mb-2">Max Price (KSh)</label>
                                    <select name="max_price" id="max_price" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200">
                                        <option value="">No Max</option>
                                        <option value="1000000">1M</option>
                                        <option value="2000000">2M</option>
                                        <option value="5000000">5M</option>
                                        <option value="10000000">10M</option>
                                        <option value="20000000">20M+</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="year" class="block text-sm font-semibold text-gray-700 mb-2">Year</label>
                                <select name="year" id="year" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200">
                                    <option value="">Any Year</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                    <option value="2022">2022</option>
                                    <option value="2021">2021</option>
                                    <option value="2020">2020</option>
                                    <option value="2019">2019</option>
                                    <option value="2018">2018</option>
                                    <option value="2017">2017</option>
                                    <option value="2016">2016</option>
                                    <option value="2015">2015</option>
                                </select>
                            </div>

                            <button type="submit" class="admin-request-add-btn group w-full text-white font-bold py-4 px-6 rounded-xl transition-all duration-500 transform hover:scale-105 shadow-xl hover:shadow-2xl font-montserrat relative overflow-hidden"
                                    style="background: linear-gradient(135deg, #DC2626 0%, #B91C1C 25%, #991B1B 50%, #7F1D1D 75%, #1F2937 100%); box-shadow: 0 8px 24px rgba(220, 38, 38, 0.4), 0 4px 12px rgba(31, 41, 55, 0.3);">
                                <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                <span class="relative z-10 flex items-center justify-center">
                                    <svg class="w-5 h-5 mr-2 transition-transform duration-300 group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    Search Cars
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- Browse by Vehicle Type Section -->
<section class="py-20 bg-primary-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="section-title-harrier mb-6">BROWSE BY VEHICLE TYPE</h2>
            <p class="text-lg text-text-light max-w-2xl mx-auto font-body">Find the perfect vehicle for your needs from our diverse collection of premium automobiles</p>
        </div>

        <!-- Vehicle Type Grid with Navigation -->
        <div class="relative">
            <!-- Navigation Controls -->
            <div class="hidden md:flex justify-between items-center absolute inset-y-0 left-0 right-0 z-10 pointer-events-none">
                <!-- Previous Button -->
                <button id="prevVehicleBtn" class="pointer-events-auto flex items-center justify-center w-12 h-12 bg-primary-white rounded-full shadow-modern-lg hover:shadow-modern-xl transition-all duration-300 hover:bg-primary-red hover:text-primary-white group disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-white disabled:hover:text-text-dark border-2 border-gray-100 hover:border-primary-red -ml-6">
                    <i class="fas fa-chevron-left text-lg group-hover:scale-110 transition-transform text-text-dark group-hover:text-primary-white"></i>
                </button>

                <!-- Next Button -->
                <button id="nextVehicleBtn" class="pointer-events-auto flex items-center justify-center w-12 h-12 bg-primary-white rounded-full shadow-modern-lg hover:shadow-modern-xl transition-all duration-300 hover:bg-primary-red hover:text-primary-white group disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary-white disabled:hover:text-text-dark border-2 border-gray-100 hover:border-primary-red -mr-6">
                    <i class="fas fa-chevron-right text-lg group-hover:scale-110 transition-transform text-text-dark group-hover:text-primary-white"></i>
                </button>
            </div>

            <!-- Vehicle Type List Container - 90% Total Width Increase (60% + 30%) -->
            <div class="overflow-hidden">
                <div id="vehicleTypeGrid" class="flex gap-8 lg:gap-12 transition-transform duration-500 ease-in-out" style="width: max-content;">
                    {% load static %}
                    {% for type in vehicle_types %}
                        <div class="flex-shrink-0 group cursor-pointer transition-all duration-300 hover:scale-105 hover:-translate-y-1 w-48 sm:w-56 md:w-64 lg:w-72 xl:w-80" onclick="window.location.href='{% url 'core:car_list' %}?vehicle_type={{ type|lower }}'">
                            <!-- Enhanced Vehicle Image - 30% Additional Width Increase -->
                            <div class="relative mb-4">
                                {% if forloop.counter <= 8 %}
                                    <img src="{% static 'images/products-images/p' %}{{ forloop.counter }}.jpg"
                                         alt="{{ type }} vehicles"
                                         class="w-full h-32 sm:h-36 md:h-44 lg:h-48 object-cover rounded-xl transition-all duration-300 group-hover:brightness-110 group-hover:shadow-modern-lg"
                                         onerror="this.parentElement.innerHTML='<div class=\'w-full h-32 sm:h-36 md:h-44 lg:h-48 bg-gradient-to-br from-primary-blue to-primary-red flex items-center justify-center rounded-xl\'><i class=\'fas fa-car text-primary-white text-3xl\'></i></div>'">
                                {% else %}
                                    <div class="w-full h-32 sm:h-36 md:h-44 lg:h-48 bg-gradient-to-br from-primary-blue to-primary-red flex items-center justify-center rounded-xl">
                                        <i class="fas fa-car text-primary-white text-3xl"></i>
                                    </div>
                                {% endif %}

                                <!-- Subtle Hover Overlay -->
                                <div class="absolute inset-0 bg-primary-red opacity-0 group-hover:opacity-10 transition-opacity duration-300 rounded-xl"></div>
                            </div>

                            <!-- Enhanced Vehicle Type Name -->
                            <div class="text-center">
                                <h3 class="text-sm sm:text-base lg:text-lg font-montserrat font-bold text-gray-800 group-hover:text-red-600 transition-all duration-500 tracking-wide uppercase letter-spacing-wider">
                                    {{ type }}
                                </h3>
                                <div class="w-8 h-0.5 bg-gradient-to-r from-red-600 to-red-700 mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-x-0 group-hover:scale-x-100"></div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Mobile Navigation Dots -->
            <div class="flex md:hidden justify-center mt-8 space-x-2">
                <div id="mobileDots" class="flex space-x-2">
                    <!-- Dots will be generated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Why Choose Us Section -->
<section class="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('{% static 'images/products-images/p1.jpg' %}'); background-size: cover; background-position: center; filter: blur(1px);"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16 animate-entrance">
            <h2 class="text-4xl md:text-5xl font-montserrat font-bold text-gray-900 mb-6">
                Why Choose
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-red-800">Gurumisha Motors</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto font-raleway leading-relaxed">Experience the difference with our comprehensive automotive services and unmatched commitment to excellence</p>
            <div class="w-24 h-1 bg-gradient-to-r from-red-600 to-red-800 mx-auto mt-6 rounded-full"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Verified Dealers -->
            <div class="group text-center animate-entrance">
                <div class="bg-white/95 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group-hover:scale-105 group-hover:-translate-y-2">
                    <div class="relative mb-6">
                        <div class="w-16 h-16 mx-auto bg-gradient-to-br from-red-600 to-red-800 rounded-xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500 shadow-lg">
                            <i class="fas fa-shield-alt text-white text-2xl"></i>
                        </div>
                        <div class="absolute -top-2 -right-6 w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-white text-xs"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-montserrat font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">Verified Dealers</h3>
                    <p class="text-gray-600 font-raleway leading-relaxed">All our dealers are thoroughly vetted and verified for your complete peace of mind and security</p>
                    <div class="mt-6 pt-4 border-t border-gray-200/50">
                        <span class="text-sm font-bold text-red-600 font-montserrat">100% Verified</span>
                    </div>
                </div>
            </div>

            <!-- Quality Inspection -->
            <div class="group text-center animate-entrance">
                <div class="bg-white/95 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group-hover:scale-105 group-hover:-translate-y-2">
                    <div class="relative mb-6">
                        <div class="w-16 h-16 mx-auto bg-gradient-to-br from-red-600 to-red-800 rounded-xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500 shadow-lg">
                            <i class="fas fa-search text-white text-2xl"></i>
                        </div>
                        <div class="absolute -top-2 -right-6 w-6 h-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-star text-white text-xs"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-montserrat font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">Quality Inspection</h3>
                    <p class="text-gray-600 font-raleway leading-relaxed">Every vehicle undergoes comprehensive quality checks and detailed inspection before listing</p>
                    <div class="mt-6 pt-4 border-t border-gray-200/50">
                        <span class="text-sm font-bold text-red-600 font-montserrat">Certified Quality</span>
                    </div>
                </div>
            </div>

            <!-- Import Services -->
            <div class="group text-center animate-entrance">
                <div class="bg-white/95 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group-hover:scale-105 group-hover:-translate-y-2">
                    <div class="relative mb-6">
                        <div class="w-16 h-16 mx-auto bg-gradient-to-br from-red-600 to-red-800 rounded-xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500 shadow-lg">
                            <i class="fas fa-shipping-fast text-white text-2xl"></i>
                        </div>
                        <div class="absolute -top-2 -right-6 w-6 h-6 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-globe text-white text-xs"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-montserrat font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">Import Services</h3>
                    <p class="text-gray-600 font-raleway leading-relaxed">Seamless car import services from Japan, UK, and other countries with full documentation</p>
                    <div class="mt-6 pt-4 border-t border-gray-200/50">
                        <span class="text-sm font-bold text-red-600 font-montserrat">Global Reach</span>
                    </div>
                </div>
            </div>

            <!-- 24/7 Support -->
            <div class="group text-center animate-entrance">
                <div class="bg-white/95 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group-hover:scale-105 group-hover:-translate-y-2">
                    <div class="relative mb-6">
                        <div class="w-16 h-16 mx-auto bg-gradient-to-br from-red-600 to-red-800 rounded-xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500 shadow-lg">
                            <i class="fas fa-headset text-white text-2xl"></i>
                        </div>
                        <div class="absolute -top-2 -right-6 w-6 h-6 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-clock text-white text-xs"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-montserrat font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">24/7 Support</h3>
                    <p class="text-gray-600 font-raleway leading-relaxed">Round-the-clock customer support to assist you at every step of your automotive journey</p>
                    <div class="mt-6 pt-4 border-t border-gray-200/50">
                        <span class="text-sm font-bold text-red-600 font-montserrat">Always Available</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Trust Indicators with Admin-Style Stats -->
        <div class="mt-16">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="admin-stat-card text-center animate-entrance hover:scale-105 transition-all duration-300">
                    <div class="flex items-center justify-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-600 to-red-800 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-users text-white text-lg"></i>
                        </div>
                    </div>
                    <div class="admin-stat-value text-red-600">2,500+</div>
                    <div class="admin-stat-label">Happy Customers</div>
                    <div class="mt-2 text-xs text-green-600 font-medium">
                        <i class="fas fa-arrow-up mr-1"></i>+15% this month
                    </div>
                </div>
                <div class="admin-stat-card text-center animate-entrance hover:scale-105 transition-all duration-300">
                    <div class="flex items-center justify-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-600 to-red-800 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-car text-white text-lg"></i>
                        </div>
                    </div>
                    <div class="admin-stat-value text-red-600">5,000+</div>
                    <div class="admin-stat-label">Cars Sold</div>
                    <div class="mt-2 text-xs text-green-600 font-medium">
                        <i class="fas fa-arrow-up mr-1"></i>+22% this month
                    </div>
                </div>
                <div class="admin-stat-card text-center animate-entrance hover:scale-105 transition-all duration-300">
                    <div class="flex items-center justify-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-600 to-red-800 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-shield-alt text-white text-lg"></i>
                        </div>
                    </div>
                    <div class="admin-stat-value text-red-600">150+</div>
                    <div class="admin-stat-label">Verified Dealers</div>
                    <div class="mt-2 text-xs text-blue-600 font-medium">
                        <i class="fas fa-check mr-1"></i>All verified
                    </div>
                </div>
                <div class="admin-stat-card text-center animate-entrance hover:scale-105 transition-all duration-300">
                    <div class="flex items-center justify-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-600 to-red-800 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-headset text-white text-lg"></i>
                        </div>
                    </div>
                    <div class="admin-stat-value text-red-600">24/7</div>
                    <div class="admin-stat-label">Customer Support</div>
                    <div class="mt-2 text-xs text-orange-600 font-medium">
                        <i class="fas fa-clock mr-1"></i>Always available
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Cars Showcase -->
<section class="py-20 bg-primary-white">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-16">
            <div class="mb-6 lg:mb-0">
                <h2 class="section-title-harrier mb-6">FEATURED CARS</h2>
                <p class="text-lg text-text-light max-w-2xl font-body">Handpicked premium vehicles from trusted dealers, featuring the latest models and best deals</p>
            </div>
            <a href="{% url 'core:car_list' %}" class="btn-harrier-secondary hidden md:inline-flex">
                <i class="fas fa-car mr-2"></i>VIEW ALL CARS
            </a>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="featured-cars">
            {% for car in featured_cars %}
                {% load promotion_tags %}
                <div class="bg-primary-white rounded-2xl shadow-modern overflow-hidden hover:shadow-modern-xl transition-all duration-500 group border border-gray-100">
                    <div class="relative overflow-hidden">
                        {% if car.main_image %}
                            <img src="{{ car.main_image.url }}" alt="{{ car.title }}" class="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500">
                        {% else %}
                            <div class="w-full h-56 bg-gradient-to-br from-accent-gray to-gray-200 flex items-center justify-center">
                                <i class="fas fa-car text-text-light text-5xl"></i>
                            </div>
                        {% endif %}

                        <!-- Enhanced Promotion Badges -->
                        {% promotion_badge car %}

                        <!-- Hot Deal Countdown -->
                        {% if car.is_hot_deal %}
                            <div class="absolute top-4 right-4">
                                {% if car.hot_deal_details %}
                                    {% with hot_deal=car.hot_deal_details %}
                                        {% hot_deal_countdown hot_deal %}
                                    {% endwith %}
                                {% else %}
                                    <div class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                                        <i class="fas fa-fire mr-1"></i>HOT DEAL
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}

                        <div class="absolute top-4 left-4">
                            <span class="bg-primary-red text-primary-white px-4 py-2 rounded-full text-sm font-bold shadow-modern">Featured</span>
                        </div>
                        <div class="absolute top-4 right-4">
                            <button class="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-crimson hover:text-white transition-colors">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="p-4">
                        <!-- Brand -->
                        <div style="font-size: 12px; color: #ed6663; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 5px; font-family: 'Open Sans', sans-serif;">{{ car.brand.name }}</div>

                        <!-- Title -->
                        <h3 class="product-title-harrier">{{ car.title }}</h3>

                        <!-- Enhanced Star Rating -->
                        <div class="flex items-center mb-2">
                            {% if car.calculated_rating > 0 %}
                                {% star_rating_display car.calculated_rating show_number=False size="text-xs" %}
                            {% else %}
                                <div class="flex text-gray-300">
                                    <i class="far fa-star text-xs"></i>
                                    <i class="far fa-star text-xs"></i>
                                    <i class="far fa-star text-xs"></i>
                                    <i class="far fa-star text-xs"></i>
                                    <i class="far fa-star text-xs"></i>
                                </div>
                            {% endif %}
                            <span class="text-xs text-harrier-muted ml-2">({{ car.views_count }} views)</span>
                        </div>

                        <!-- Other Info -->
                        <div class="other-info">
                            <div class="col-km" style="float: left; width: 33.3%;">
                                <i class="fas fa-tachometer-alt" style="display: block; margin-bottom: 5px; color: #ccc;"></i>
                                {{ car.mileage|floatformat:0 }} km
                            </div>
                            <div class="col-engine" style="float: left; width: 33.3%;">
                                <i class="fas fa-gas-pump" style="display: block; margin-bottom: 5px; color: #ccc;"></i>
                                {{ car.fuel_type|title }}
                            </div>
                            <div class="col-date" style="float: left; width: 33.3%; font-size: 14px;">
                                <i class="fas fa-calendar" style="display: block; margin-bottom: 5px; color: #ccc;"></i>
                                {{ car.year }}
                            </div>
                            <div style="clear: both;"></div>
                        </div>

                        <!-- Price and Actions -->
                        <div class="flex justify-between items-center pt-3 border-t border-gray-100 mt-4">
                            <div>
                                <span class="price-harrier">KSh {{ car.price|floatformat:0 }}</span>
                            </div>
                            <div class="flex space-x-2">
                                <button class="w-8 h-8 border border-gray-300 rounded flex items-center justify-center hover:border-harrier-red hover:text-harrier-red transition-colors" title="Compare">
                                    <i class="fas fa-balance-scale text-xs"></i>
                                </button>
                                <a href="{% url 'core:car_detail' car.pk %}" class="btn-harrier-primary px-4 py-2 text-sm">VIEW</a>
                            </div>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="col-span-full text-center py-12">
                    <i class="fas fa-car text-gray-300 text-6xl mb-4"></i>
                    <p class="text-gray-500 text-lg">No featured cars available at the moment.</p>
                </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-8 md:hidden">
            <a href="{% url 'core:car_list' %}" class="btn-secondary">View All Cars</a>
        </div>
    </div>
</section>

<!-- Car Brands Display -->
<section class="py-20 bg-primary-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="section-title-harrier mb-6">TRUSTED BRANDS</h2>
            <p class="text-lg text-text-light max-w-2xl mx-auto font-body">We work with the world's leading automotive brands to bring you quality and reliability</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
            {% for brand in car_brands %}
                <div class="text-center group cursor-pointer">
                    <div class="bg-accent-gray rounded-2xl p-8 hover:bg-primary-white hover:shadow-modern transition-all duration-300 border border-gray-100">
                        {% if brand.logo %}
                            <img src="{{ brand.logo.url }}" alt="{{ brand.name }}" class="w-20 h-20 mx-auto object-contain group-hover:scale-110 transition-transform duration-300">
                        {% else %}
                            <div class="w-20 h-20 mx-auto bg-gradient-to-br from-primary-blue to-primary-red rounded-xl flex items-center justify-center">
                                <span class="text-primary-white font-bold text-lg">{{ brand.name|slice:":2" }}</span>
                            </div>
                        {% endif %}
                        <p class="mt-4 text-base font-semibold text-text-dark group-hover:text-primary-red transition-colors">{{ brand.name }}</p>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Enhanced Sell Your Car Section -->
<section class="py-20 relative overflow-hidden">
    <!-- Enhanced Background with Glassmorphism -->
    <div class="absolute inset-0">
        <img src="{% static 'images/slide2.jpg' %}" alt="Sell Your Car" class="w-full h-full object-cover">
        <div class="absolute inset-0 bg-gradient-to-br from-black/70 via-black/60 to-black/70"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-red-900/30 to-blue-900/30"></div>
    </div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center text-white mb-16">
            <h2 class="text-4xl md:text-5xl font-montserrat font-bold mb-6">
                <span class="inline-block bg-gradient-to-r from-red-500 to-red-700 text-transparent bg-clip-text px-6 py-3 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20">
                    READY TO SELL YOUR CAR?
                </span>
            </h2>
            <p class="text-xl font-raleway leading-relaxed max-w-3xl mx-auto mb-8 text-gray-100">
                Get the best value for your vehicle with our trusted platform. List your car today and reach thousands of potential buyers across Kenya.
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-red-500 to-red-700 mx-auto rounded-full"></div>
        </div>

        <!-- Enhanced Process Steps -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div class="group text-center">
                <div class="relative mb-8">
                    <div class="w-24 h-24 mx-auto bg-white/10 backdrop-blur-md rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-2xl border border-white/20 group-hover:bg-white/20">
                        <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-700 rounded-xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500">
                            <i class="fas fa-camera text-white text-2xl"></i>
                        </div>
                    </div>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                </div>
                <h3 class="text-xl font-montserrat font-bold mb-4 group-hover:text-red-400 transition-colors duration-300">UPLOAD PHOTOS</h3>
                <p class="text-gray-200 font-raleway leading-relaxed">Add high-quality photos of your vehicle to attract more buyers and showcase your car's best features</p>
            </div>

            <div class="group text-center">
                <div class="relative mb-8">
                    <div class="w-24 h-24 mx-auto bg-white/10 backdrop-blur-md rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-2xl border border-white/20 group-hover:bg-white/20">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500">
                            <i class="fas fa-edit text-white text-2xl"></i>
                        </div>
                    </div>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                </div>
                <h3 class="text-xl font-montserrat font-bold mb-4 group-hover:text-blue-400 transition-colors duration-300">ADD DETAILS</h3>
                <p class="text-gray-200 font-raleway leading-relaxed">Provide comprehensive vehicle information, specifications, and maintenance history for transparency</p>
            </div>

            <div class="group text-center">
                <div class="relative mb-8">
                    <div class="w-24 h-24 mx-auto bg-white/10 backdrop-blur-md rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-2xl border border-white/20 group-hover:bg-white/20">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-700 rounded-xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500">
                            <i class="fas fa-handshake text-white text-2xl"></i>
                        </div>
                    </div>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                </div>
                <h3 class="text-xl font-montserrat font-bold mb-4 group-hover:text-green-400 transition-colors duration-300">GET OFFERS</h3>
                <p class="text-gray-200 font-raleway leading-relaxed">Receive inquiries and offers from thousands of interested buyers across our platform</p>
            </div>
        </div>

        <!-- Enhanced Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-6 justify-center">
            <a href="{% url 'core:sell_car' %}" class="group inline-flex items-center justify-center px-10 py-4 text-lg font-bold text-white rounded-2xl transition-all duration-500 transform hover:scale-110 hover:shadow-2xl font-montserrat relative overflow-hidden bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800">
                <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <i class="fas fa-car mr-3 transition-transform duration-300 group-hover:rotate-12"></i>
                <span class="relative z-10">SELL YOUR CAR</span>
            </a>
            <button id="get-valuation-btn" class="group inline-flex items-center justify-center px-10 py-4 text-lg font-bold text-white border-3 border-white/80 rounded-2xl transition-all duration-500 hover:bg-white hover:text-gray-900 font-montserrat relative overflow-hidden backdrop-blur-sm bg-white/10">
                <div class="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <i class="fas fa-calculator mr-3 transition-transform duration-300 group-hover:rotate-12"></i>
                <span class="relative z-10">GET VALUATION</span>
            </button>
        </div>
    </div>
</section>

<!-- Get Valuation Modal -->
<div id="valuation-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-md w-full border border-gray-200/50 overflow-hidden">
            <div class="bg-gradient-to-r from-red-600 to-red-700 p-6 text-white">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-montserrat font-bold">Get Car Valuation</h3>
                    <button id="close-valuation-modal" class="text-white hover:text-gray-200 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <p class="text-red-100 mt-2 font-raleway">Get an instant estimate of your car's value</p>
            </div>

            <form id="valuation-form" class="p-6 space-y-4" hx-post="/get-valuation/" hx-target="#valuation-result">
                {% csrf_token %}
                <div>
                    <label class="block text-gray-700 font-medium mb-2 font-montserrat">Car Make</label>
                    <input type="text" name="make" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-300 font-inter">
                </div>

                <div>
                    <label class="block text-gray-700 font-medium mb-2 font-montserrat">Model</label>
                    <input type="text" name="model" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-300 font-inter">
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 font-medium mb-2 font-montserrat">Year</label>
                        <input type="number" name="year" required min="1990" max="2024" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-300 font-inter">
                    </div>

                    <div>
                        <label class="block text-gray-700 font-medium mb-2 font-montserrat">Mileage (km)</label>
                        <input type="number" name="mileage" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-300 font-inter">
                    </div>
                </div>

                <div>
                    <label class="block text-gray-700 font-medium mb-2 font-montserrat">Condition</label>
                    <select name="condition" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-300 font-inter">
                        <option value="">Select Condition</option>
                        <option value="excellent">Excellent</option>
                        <option value="good">Good</option>
                        <option value="fair">Fair</option>
                        <option value="poor">Poor</option>
                    </select>
                </div>

                <div id="valuation-result" class="mt-4"></div>

                <button type="submit" class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white font-bold py-3 px-6 rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-montserrat">
                    Get Valuation
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Hot Deals Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-12">
            <div>
                <h2 class="text-3xl md:text-4xl font-heading font-bold text-midnight mb-4">Hot Deals</h2>
                <p class="text-lg text-gray-600">Limited time offers on premium vehicles</p>
            </div>
            <div class="hidden md:flex items-center space-x-4">
                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-crimson hover:text-white transition-colors" id="deals-prev">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-crimson hover:text-white transition-colors" id="deals-next">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="hot-deals">
            {% for car in hot_deals %}
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group" data-deal-id="{% if car.hot_deal_details %}{{ car.hot_deal_details.id }}{% endif %}">
                    <div class="relative">
                        {% if car.main_image %}
                            <img src="{{ car.main_image.url }}" alt="{{ car.title }}" class="w-full h-40 object-cover group-hover:scale-110 transition-transform duration-500">
                        {% else %}
                            <div class="w-full h-40 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-car text-gray-400 text-3xl"></i>
                            </div>
                        {% endif %}

                        <!-- Enhanced Hot Deal Badge -->
                        <div class="absolute top-3 left-3">
                            <span class="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg animate-pulse">
                                <i class="fas fa-fire mr-1"></i>HOT DEAL
                            </span>
                        </div>

                        <!-- Countdown Timer -->
                        {% if car.hot_deal_details %}
                            <div class="absolute top-3 right-3">
                                <div class="countdown-timer"
                                     data-countdown-end="{{ car.hot_deal_details.end_date|date:'c' }}"
                                     data-deal-id="{{ car.hot_deal_details.id }}">
                                    {% hot_deal_countdown car.hot_deal_details %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Discount Badge -->
                        {% if car.hot_deal_details %}
                            <div class="absolute bottom-3 left-3">
                                <div class="bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-bold">
                                    {% if car.hot_deal_details.discount_type == 'percentage' %}
                                        {{ car.hot_deal_details.discount_value|floatformat:0 }}% OFF
                                    {% else %}
                                        KSh {{ car.hot_deal_details.discount_value|floatformat:0 }} OFF
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>

                    <div class="p-4">
                        <h3 class="font-semibold text-midnight mb-1 truncate">{{ car.title }}</h3>
                        <p class="text-sm text-gray-600 mb-2">{{ car.year }} • {{ car.mileage|floatformat:0 }} km</p>

                        <!-- Star Rating -->
                        {% if car.calculated_rating > 0 %}
                            <div class="mb-3">
                                {% star_rating_display car.calculated_rating show_number=False size="text-xs" %}
                            </div>
                        {% endif %}

                        <div class="flex justify-between items-center">
                            <div>
                                {% if car.hot_deal_details %}
                                    <div class="flex items-center space-x-2">
                                        <span class="text-lg font-bold text-crimson">KSh {{ car.hot_deal_details.discounted_price|floatformat:0 }}</span>
                                        <span class="text-sm text-gray-500 line-through">KSh {{ car.hot_deal_details.original_price|floatformat:0 }}</span>
                                    </div>
                                    <p class="text-xs text-green-600 font-medium">
                                        Save: KSh {{ car.hot_deal_details.original_price|sub:car.hot_deal_details.discounted_price|floatformat:0 }}
                                    </p>
                                {% else %}
                                    <span class="text-lg font-bold text-crimson">KSh {{ car.price|floatformat:0 }}</span>
                                {% endif %}
                            </div>
                            <div class="flex space-x-2">
                                {% if car.hot_deal_details %}
                                    <a href="{% url 'core:hot_deal_detail' car.hot_deal_details.id %}" class="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 transition-colors">Deal</a>
                                {% endif %}
                                <a href="{% url 'core:car_detail' car.pk %}" class="text-xs bg-crimson text-white px-3 py-1 rounded hover:bg-electric-red transition-colors">View</a>
                            </div>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500">No hot deals available at the moment.</p>
                </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Enhanced Genuine Spare Parts Section -->
<section class="py-20 bg-gradient-to-br from-white via-gray-50 to-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('{% static 'images/products-images/p3.jpg' %}'); background-size: cover; background-position: center; filter: blur(1px);"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16 animate-entrance">
            <h2 class="text-4xl md:text-5xl font-montserrat font-bold text-gray-900 mb-6">
                Genuine
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-red-800">Spare Parts</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto font-raleway leading-relaxed">Quality spare parts for all major car brands. Keep your vehicle running smoothly with our authentic, certified automotive components.</p>
            <div class="w-24 h-1 bg-gradient-to-r from-red-600 to-red-800 mx-auto mt-6 rounded-full"></div>
        </div>

        <!-- Featured Spare Parts Categories -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <!-- Engine Parts -->
            <div class="group cursor-pointer animate-entrance" onclick="window.location.href='{% url 'core:spare_parts' %}?category=engine'">
                <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 text-center hover:bg-white transition-all duration-500 shadow-xl hover:shadow-2xl border border-gray-200/50 group-hover:scale-105 group-hover:-translate-y-2">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 mx-auto bg-gradient-to-br from-red-500 to-red-700 rounded-2xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500 shadow-lg">
                            <i class="fas fa-cog text-white text-3xl"></i>
                        </div>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-white text-xs"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-montserrat font-bold text-gray-900 mb-3 group-hover:text-red-600 transition-colors duration-300">Engine Parts</h3>
                    <p class="text-gray-600 font-raleway leading-relaxed">High-performance engine components and accessories</p>
                    <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <span class="text-red-600 font-medium">View Parts →</span>
                    </div>
                </div>
            </div>

            <!-- Brake System -->
            <div class="group cursor-pointer animate-entrance" onclick="window.location.href='{% url 'core:spare_parts' %}?category=brakes'">
                <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 text-center hover:bg-white transition-all duration-500 shadow-xl hover:shadow-2xl border border-gray-200/50 group-hover:scale-105 group-hover:-translate-y-2">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 to-blue-700 rounded-2xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500 shadow-lg">
                            <i class="fas fa-circle-notch text-white text-3xl"></i>
                        </div>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-xs"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-montserrat font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">Brake System</h3>
                    <p class="text-gray-600 font-raleway leading-relaxed">Safety-certified brake pads, discs, and components</p>
                    <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <span class="text-blue-600 font-medium">View Parts →</span>
                    </div>
                </div>
            </div>

            <!-- Electrical -->
            <div class="group cursor-pointer animate-entrance" onclick="window.location.href='{% url 'core:spare_parts' %}?category=electrical'">
                <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 text-center hover:bg-white transition-all duration-500 shadow-xl hover:shadow-2xl border border-gray-200/50 group-hover:scale-105 group-hover:-translate-y-2">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 mx-auto bg-gradient-to-br from-yellow-500 to-yellow-700 rounded-2xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500 shadow-lg">
                            <i class="fas fa-bolt text-white text-3xl"></i>
                        </div>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-star text-white text-xs"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-montserrat font-bold text-gray-900 mb-3 group-hover:text-yellow-600 transition-colors duration-300">Electrical</h3>
                    <p class="text-gray-600 font-raleway leading-relaxed">Batteries, alternators, and electrical systems</p>
                    <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <span class="text-yellow-600 font-medium">View Parts →</span>
                    </div>
                </div>
            </div>

            <!-- Body Parts -->
            <div class="group cursor-pointer animate-entrance" onclick="window.location.href='{% url 'core:spare_parts' %}?category=body'">
                <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 text-center hover:bg-white transition-all duration-500 shadow-xl hover:shadow-2xl border border-gray-200/50 group-hover:scale-105 group-hover:-translate-y-2">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 mx-auto bg-gradient-to-br from-purple-500 to-purple-700 rounded-2xl flex items-center justify-center group-hover:rotate-6 transition-transform duration-500 shadow-lg">
                            <i class="fas fa-car text-white text-3xl"></i>
                        </div>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-wrench text-white text-xs"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-montserrat font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors duration-300">Body Parts</h3>
                    <p class="text-gray-600 font-raleway leading-relaxed">Panels, bumpers, lights, and exterior components</p>
                    <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <span class="text-purple-600 font-medium">View Parts →</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Products Showcase -->
        <div class="bg-white/60 backdrop-blur-md rounded-3xl p-8 border border-gray-200/50 shadow-2xl mb-12">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-montserrat font-bold text-gray-900 mb-4">Featured Products</h3>
                <p class="text-gray-600 font-raleway">Top-quality spare parts from trusted manufacturers</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center group">
                    <div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-md">
                        <img src="{% static 'images/products-images/p4.jpg' %}" alt="Brake Pads" class="w-12 h-12 object-cover rounded-lg">
                    </div>
                    <p class="text-sm font-medium text-gray-700">Brake Pads</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-md">
                        <img src="{% static 'images/products-images/p5.jpg' %}" alt="Oil Filters" class="w-12 h-12 object-cover rounded-lg">
                    </div>
                    <p class="text-sm font-medium text-gray-700">Oil Filters</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-md">
                        <img src="{% static 'images/products-images/p6.jpg' %}" alt="Spark Plugs" class="w-12 h-12 object-cover rounded-lg">
                    </div>
                    <p class="text-sm font-medium text-gray-700">Spark Plugs</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-md">
                        <img src="{% static 'images/products-images/p7.jpg' %}" alt="Batteries" class="w-12 h-12 object-cover rounded-lg">
                    </div>
                    <p class="text-sm font-medium text-gray-700">Batteries</p>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <a href="{% url 'core:spare_parts' %}" class="group inline-flex items-center justify-center px-10 py-4 text-lg font-bold text-white rounded-2xl transition-all duration-500 transform hover:scale-110 hover:shadow-2xl font-montserrat relative overflow-hidden bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800">
                <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <i class="fas fa-cog mr-3 transition-transform duration-300 group-hover:rotate-180"></i>
                <span class="relative z-10">Browse All Spare Parts</span>
            </a>
        </div>
    </div>
</section>

<!-- Enhanced Customer Testimonials with Carousel -->
<section class="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('{% static 'images/products-images/p2.jpg' %}'); background-size: cover; background-position: center; filter: blur(1px);"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16 animate-entrance">
            <h2 class="text-4xl md:text-5xl font-montserrat font-bold text-gray-900 mb-6">
                What Our
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-red-800">Customers Say</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto font-raleway leading-relaxed">Real experiences from satisfied customers who trust Gurumisha Motors for their automotive needs</p>
            <div class="w-24 h-1 bg-gradient-to-r from-red-600 to-red-800 mx-auto mt-6 rounded-full"></div>
        </div>

        <!-- Testimonials Carousel Container -->
        <div class="relative">
            <!-- Navigation Controls -->
            <div class="hidden md:flex justify-between items-center absolute inset-y-0 left-0 right-0 z-10 pointer-events-none">
                <!-- Previous Button -->
                <button id="prevTestimonialBtn" class="pointer-events-auto flex items-center justify-center w-12 h-12 bg-white/90 backdrop-blur-md rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 hover:bg-red-600 hover:text-white group disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200/50 -ml-6">
                    <i class="fas fa-chevron-left text-lg group-hover:scale-110 transition-transform text-gray-700 group-hover:text-white"></i>
                </button>

                <!-- Next Button -->
                <button id="nextTestimonialBtn" class="pointer-events-auto flex items-center justify-center w-12 h-12 bg-white/90 backdrop-blur-md rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 hover:bg-red-600 hover:text-white group disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200/50 -mr-6">
                    <i class="fas fa-chevron-right text-lg group-hover:scale-110 transition-transform text-gray-700 group-hover:text-white"></i>
                </button>
            </div>

            <!-- Testimonials Container -->
            <div class="overflow-hidden">
                <div id="testimonialsGrid" class="flex gap-8 transition-transform duration-500 ease-in-out" style="width: max-content;">
                    <!-- Testimonial 1 - Car Purchase -->
                    <div class="flex-shrink-0 w-80 md:w-96">
                        <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group hover:scale-105 hover:-translate-y-2">
                            <!-- Rating Stars -->
                            <div class="flex items-center mb-6">
                                <div class="flex text-yellow-400 mr-3">
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-600 bg-yellow-50 px-3 py-1 rounded-full">5.0</span>
                            </div>

                            <!-- Testimonial Content -->
                            <p class="text-gray-700 mb-6 italic font-raleway leading-relaxed text-lg">"Exceptional service! I found my dream Toyota Camry through Gurumisha Motors. The entire process was smooth, transparent, and professional. The team went above and beyond to ensure I got the best deal."</p>

                            <!-- Customer Info -->
                            <div class="flex items-center">
                                <div class="w-14 h-14 bg-gradient-to-br from-red-500 to-red-700 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    <img src="{% static 'images/testimonials/customer1.jpg' %}" alt="Sarah Wanjiku" class="w-full h-full object-cover rounded-full" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <span style="display: none;">SW</span>
                                </div>
                                <div class="ml-4">
                                    <p class="font-bold text-gray-900 font-montserrat">Sarah Wanjiku</p>
                                    <p class="text-sm text-gray-600 font-raleway">Car Purchase • Nairobi</p>
                                    <p class="text-xs text-red-600 font-medium">Verified Customer</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 2 - Import Service -->
                    <div class="flex-shrink-0 w-80 md:w-96">
                        <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group hover:scale-105 hover:-translate-y-2">
                            <!-- Rating Stars -->
                            <div class="flex items-center mb-6">
                                <div class="flex text-yellow-400 mr-3">
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-600 bg-yellow-50 px-3 py-1 rounded-full">5.0</span>
                            </div>

                            <!-- Testimonial Content -->
                            <p class="text-gray-700 mb-6 italic font-raleway leading-relaxed text-lg">"Outstanding import service! They handled everything from Japan to Kenya seamlessly. Real-time tracking, transparent pricing, and my BMW arrived exactly as described. Highly recommended!"</p>

                            <!-- Customer Info -->
                            <div class="flex items-center">
                                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    <img src="{% static 'images/testimonials/customer2.jpg' %}" alt="James Mwangi" class="w-full h-full object-cover rounded-full" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <span style="display: none;">JM</span>
                                </div>
                                <div class="ml-4">
                                    <p class="font-bold text-gray-900 font-montserrat">James Mwangi</p>
                                    <p class="text-sm text-gray-600 font-raleway">Import Service • Mombasa</p>
                                    <p class="text-xs text-red-600 font-medium">Verified Customer</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 3 - Spare Parts -->
                    <div class="flex-shrink-0 w-80 md:w-96">
                        <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group hover:scale-105 hover:-translate-y-2">
                            <!-- Rating Stars -->
                            <div class="flex items-center mb-6">
                                <div class="flex text-yellow-400 mr-3">
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="far fa-star text-lg"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-600 bg-yellow-50 px-3 py-1 rounded-full">4.0</span>
                            </div>

                            <!-- Testimonial Content -->
                            <p class="text-gray-700 mb-6 italic font-raleway leading-relaxed text-lg">"Genuine spare parts at competitive prices! Quick delivery and excellent customer support. My Mercedes runs like new again. The quality assurance gives me complete peace of mind."</p>

                            <!-- Customer Info -->
                            <div class="flex items-center">
                                <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-700 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    <img src="{% static 'images/testimonials/customer3.jpg' %}" alt="Grace Akinyi" class="w-full h-full object-cover rounded-full" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <span style="display: none;">GA</span>
                                </div>
                                <div class="ml-4">
                                    <p class="font-bold text-gray-900 font-montserrat">Grace Akinyi</p>
                                    <p class="text-sm text-gray-600 font-raleway">Spare Parts • Kisumu</p>
                                    <p class="text-xs text-red-600 font-medium">Verified Customer</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 4 - Dealer Service -->
                    <div class="flex-shrink-0 w-80 md:w-96">
                        <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group hover:scale-105 hover:-translate-y-2">
                            <!-- Rating Stars -->
                            <div class="flex items-center mb-6">
                                <div class="flex text-yellow-400 mr-3">
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-600 bg-yellow-50 px-3 py-1 rounded-full">5.0</span>
                            </div>

                            <!-- Testimonial Content -->
                            <p class="text-gray-700 mb-6 italic font-raleway leading-relaxed text-lg">"Professional dealer network and excellent financing options. They helped me secure the best loan terms for my Nissan X-Trail. The after-sales support is exceptional!"</p>

                            <!-- Customer Info -->
                            <div class="flex items-center">
                                <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    <img src="{% static 'images/testimonials/customer4.jpg' %}" alt="David Kiprop" class="w-full h-full object-cover rounded-full" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <span style="display: none;">DK</span>
                                </div>
                                <div class="ml-4">
                                    <p class="font-bold text-gray-900 font-montserrat">David Kiprop</p>
                                    <p class="text-sm text-gray-600 font-raleway">Dealer Service • Eldoret</p>
                                    <p class="text-xs text-red-600 font-medium">Verified Customer</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 5 - Sell Service -->
                    <div class="flex-shrink-0 w-80 md:w-96">
                        <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group hover:scale-105 hover:-translate-y-2">
                            <!-- Rating Stars -->
                            <div class="flex items-center mb-6">
                                <div class="flex text-yellow-400 mr-3">
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="far fa-star text-lg"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-600 bg-yellow-50 px-3 py-1 rounded-full">4.0</span>
                            </div>

                            <!-- Testimonial Content -->
                            <p class="text-gray-700 mb-6 italic font-raleway leading-relaxed text-lg">"Sold my Honda Civic quickly and at a fair price. The platform connected me with serious buyers, and the transaction was secure. Great experience overall!"</p>

                            <!-- Customer Info -->
                            <div class="flex items-center">
                                <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-orange-700 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    <img src="{% static 'images/testimonials/customer5.jpg' %}" alt="Mary Njeri" class="w-full h-full object-cover rounded-full" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <span style="display: none;">MN</span>
                                </div>
                                <div class="ml-4">
                                    <p class="font-bold text-gray-900 font-montserrat">Mary Njeri</p>
                                    <p class="text-sm text-gray-600 font-raleway">Car Seller • Nakuru</p>
                                    <p class="text-xs text-red-600 font-medium">Verified Customer</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial 6 - Auction Service -->
                    <div class="flex-shrink-0 w-80 md:w-96">
                        <div class="bg-white/80 backdrop-blur-md rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-200/50 group hover:scale-105 hover:-translate-y-2">
                            <!-- Rating Stars -->
                            <div class="flex items-center mb-6">
                                <div class="flex text-yellow-400 mr-3">
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                    <i class="fas fa-star text-lg"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-600 bg-yellow-50 px-3 py-1 rounded-full">5.0</span>
                            </div>

                            <!-- Testimonial Content -->
                            <p class="text-gray-700 mb-6 italic font-raleway leading-relaxed text-lg">"Amazing auction experience! Won a fantastic Subaru Forester at an unbeatable price. The bidding process was transparent and fair. Highly satisfied with my purchase!"</p>

                            <!-- Customer Info -->
                            <div class="flex items-center">
                                <div class="w-14 h-14 bg-gradient-to-br from-indigo-500 to-indigo-700 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    <img src="{% static 'images/testimonials/customer6.jpg' %}" alt="Peter Otieno" class="w-full h-full object-cover rounded-full" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <span style="display: none;">PO</span>
                                </div>
                                <div class="ml-4">
                                    <p class="font-bold text-gray-900 font-montserrat">Peter Otieno</p>
                                    <p class="text-sm text-gray-600 font-raleway">Auction Winner • Thika</p>
                                    <p class="text-xs text-red-600 font-medium">Verified Customer</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Navigation Dots -->
            <div class="flex md:hidden justify-center mt-8 space-x-2">
                <div id="testimonialDots" class="flex space-x-2">
                    <!-- Dots will be generated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Trust Indicators -->
        <div class="mt-16 text-center">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="animate-entrance">
                    <div class="text-3xl font-bold text-red-600 font-montserrat">4.8/5</div>
                    <div class="text-gray-600 font-raleway">Average Rating</div>
                </div>
                <div class="animate-entrance">
                    <div class="text-3xl font-bold text-red-600 font-montserrat">2,500+</div>
                    <div class="text-gray-600 font-raleway">Happy Customers</div>
                </div>
                <div class="animate-entrance">
                    <div class="text-3xl font-bold text-red-600 font-montserrat">98%</div>
                    <div class="text-gray-600 font-raleway">Satisfaction Rate</div>
                </div>
                <div class="animate-entrance">
                    <div class="text-3xl font-bold text-red-600 font-montserrat">24/7</div>
                    <div class="text-gray-600 font-raleway">Customer Support</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Resources Preview -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-12">
            <div>
                <h2 class="text-3xl md:text-4xl font-heading font-bold text-midnight mb-4">Latest Resources</h2>
                <p class="text-lg text-gray-600">Stay updated with automotive news, tips, and insights</p>
            </div>
            <a href="{% url 'core:resources' %}" class="btn-secondary hidden md:inline-flex">View All Resources</a>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            {% for post in blog_posts %}
                <article class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                    <div class="relative overflow-hidden">
                        {% if post.featured_image %}
                            <img src="{{ post.featured_image.url }}" alt="{{ post.title }}" class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500">
                        {% else %}
                            <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <i class="fas fa-newspaper text-gray-400 text-4xl"></i>
                            </div>
                        {% endif %}
                    </div>

                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 mb-3">
                            <i class="fas fa-calendar mr-2"></i>
                            <span>{{ post.published_at|date:"M d, Y" }}</span>
                            <span class="mx-2">•</span>
                            <span>{{ post.author.get_full_name|default:post.author.username }}</span>
                        </div>

                        <h3 class="text-xl font-semibold text-midnight mb-3 group-hover:text-crimson transition-colors">
                            <a href="{% url 'core:resource_detail' post.slug %}">{{ post.title }}</a>
                        </h3>

                        <p class="text-gray-600 mb-4">{{ post.excerpt|default:post.content|truncatewords:20 }}</p>

                        <a href="{% url 'core:resource_detail' post.slug %}" class="text-crimson font-semibold hover:text-electric-red transition-colors">
                            Read More <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </article>
            {% empty %}
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500">No resources available yet.</p>
                </div>
            {% endfor %}
        </div>

        <div class="text-center mt-8 md:hidden">
            <a href="{% url 'core:resources' %}" class="btn-secondary">View All Resources</a>
        </div>
    </div>
</section>



<!-- Enhanced Vehicle Type List Navigation JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Testimonials Carousel
    const testimonialsGrid = document.getElementById('testimonialsGrid');
    const prevTestimonialBtn = document.getElementById('prevTestimonialBtn');
    const nextTestimonialBtn = document.getElementById('nextTestimonialBtn');
    const testimonialDots = document.getElementById('testimonialDots');
    const testimonialContainer = testimonialsGrid?.parentElement;

    if (testimonialsGrid && testimonialContainer) {
        const testimonialItems = Array.from(testimonialsGrid.children);
        const totalTestimonials = testimonialItems.length;
        let currentTestimonialIndex = 0;
        let testimonialScrollAmount = 0;

        function getTestimonialScrollAmount() {
            const width = window.innerWidth;
            if (width >= 768) return 416; // md: scroll by ~1 item (w-96 + gap = 416px)
            return 336; // mobile: scroll by ~1 item (w-80 + gap = 336px)
        }

        function getVisibleTestimonials() {
            const containerWidth = testimonialContainer.offsetWidth;
            const itemWidth = getTestimonialScrollAmount();
            return Math.floor(containerWidth / itemWidth);
        }

        function getMaxTestimonialScroll() {
            const containerWidth = testimonialContainer.offsetWidth;
            const totalWidth = testimonialsGrid.scrollWidth;
            return Math.max(0, totalWidth - containerWidth);
        }

        function getTotalTestimonialPages() {
            const visibleItems = getVisibleTestimonials();
            return Math.ceil(totalTestimonials / visibleItems);
        }

        function updateTestimonialNavigation() {
            const maxScroll = getMaxTestimonialScroll();

            if (prevTestimonialBtn && nextTestimonialBtn) {
                prevTestimonialBtn.disabled = testimonialScrollAmount <= 0;
                nextTestimonialBtn.disabled = testimonialScrollAmount >= maxScroll;

                if (prevTestimonialBtn.disabled) {
                    prevTestimonialBtn.classList.add('opacity-50', 'cursor-not-allowed');
                } else {
                    prevTestimonialBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                }

                if (nextTestimonialBtn.disabled) {
                    nextTestimonialBtn.classList.add('opacity-50', 'cursor-not-allowed');
                } else {
                    nextTestimonialBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            }

            updateTestimonialDots();
            testimonialsGrid.style.transform = `translateX(-${testimonialScrollAmount}px)`;
        }

        function updateTestimonialDots() {
            if (!testimonialDots) return;

            const totalPages = getTotalTestimonialPages();
            const visibleItems = getVisibleTestimonials();
            const currentPage = Math.floor(currentTestimonialIndex / visibleItems);

            testimonialDots.innerHTML = '';

            if (totalPages > 1) {
                for (let i = 0; i < totalPages; i++) {
                    const dot = document.createElement('button');
                    dot.className = `w-2 h-2 rounded-full transition-all duration-300 ${
                        i === currentPage
                            ? 'bg-red-600 w-6'
                            : 'bg-gray-300 hover:bg-gray-400'
                    }`;
                    dot.addEventListener('click', () => goToTestimonialPage(i));
                    testimonialDots.appendChild(dot);
                }
            }
        }

        function goToTestimonialPage(page) {
            const visibleItems = getVisibleTestimonials();
            const newIndex = page * visibleItems;
            const itemScrollAmount = getTestimonialScrollAmount();

            currentTestimonialIndex = Math.min(newIndex, totalTestimonials - 1);
            testimonialScrollAmount = Math.min(currentTestimonialIndex * itemScrollAmount, getMaxTestimonialScroll());

            updateTestimonialNavigation();
        }

        function goToPreviousTestimonial() {
            const itemScrollAmount = getTestimonialScrollAmount();
            const newScrollAmount = Math.max(0, testimonialScrollAmount - itemScrollAmount);

            if (newScrollAmount !== testimonialScrollAmount) {
                testimonialScrollAmount = newScrollAmount;
                currentTestimonialIndex = Math.max(0, currentTestimonialIndex - 1);
                updateTestimonialNavigation();
            }
        }

        function goToNextTestimonial() {
            const itemScrollAmount = getTestimonialScrollAmount();
            const maxScroll = getMaxTestimonialScroll();
            const newScrollAmount = Math.min(maxScroll, testimonialScrollAmount + itemScrollAmount);

            if (newScrollAmount !== testimonialScrollAmount) {
                testimonialScrollAmount = newScrollAmount;
                currentTestimonialIndex = Math.min(totalTestimonials - 1, currentTestimonialIndex + 1);
                updateTestimonialNavigation();
            }
        }

        // Event listeners for testimonials
        if (prevTestimonialBtn) {
            prevTestimonialBtn.addEventListener('click', function(e) {
                e.preventDefault();
                goToPreviousTestimonial();
            });
        }

        if (nextTestimonialBtn) {
            nextTestimonialBtn.addEventListener('click', function(e) {
                e.preventDefault();
                goToNextTestimonial();
            });
        }

        // Auto-scroll for testimonials
        let testimonialAutoScrollInterval;

        function startTestimonialAutoScroll() {
            testimonialAutoScrollInterval = setInterval(function() {
                const maxScroll = getMaxTestimonialScroll();
                if (testimonialScrollAmount >= maxScroll) {
                    testimonialScrollAmount = 0;
                    currentTestimonialIndex = 0;
                } else {
                    goToNextTestimonial();
                }
            }, 5000);
        }

        function stopTestimonialAutoScroll() {
            if (testimonialAutoScrollInterval) {
                clearInterval(testimonialAutoScrollInterval);
            }
        }

        // Initialize testimonials carousel
        updateTestimonialNavigation();
        startTestimonialAutoScroll();

        // Pause auto-scroll on hover
        const testimonialSection = testimonialContainer.closest('section');
        if (testimonialSection) {
            testimonialSection.addEventListener('mouseenter', stopTestimonialAutoScroll);
            testimonialSection.addEventListener('mouseleave', startTestimonialAutoScroll);
        }

        // Handle window resize for testimonials
        window.addEventListener('resize', function() {
            testimonialScrollAmount = 0;
            currentTestimonialIndex = 0;
            updateTestimonialNavigation();
        });
    }

    // Vehicle Type Grid Navigation
    const vehicleTypeGrid = document.getElementById('vehicleTypeGrid');
    const prevBtn = document.getElementById('prevVehicleBtn');
    const nextBtn = document.getElementById('nextVehicleBtn');
    const mobileDots = document.getElementById('mobileDots');
    const container = vehicleTypeGrid?.parentElement;

    if (!vehicleTypeGrid || !container) return;

    const items = Array.from(vehicleTypeGrid.children);
    const totalItems = items.length;
    let currentIndex = 0;
    let scrollAmount = 0;

    // Calculate scroll amount based on screen size and item width
    function getScrollAmount() {
        const width = window.innerWidth;
        if (width >= 1280) return 320; // xl: scroll by ~1 item (w-80 = 320px)
        if (width >= 1024) return 288; // lg: scroll by ~1 item (w-72 = 288px)
        if (width >= 768) return 256;  // md: scroll by ~1 item (w-64 = 256px)
        if (width >= 640) return 224;  // sm: scroll by ~1 item (w-56 = 224px)
        return 192; // mobile: scroll by ~1 item (w-48 = 192px)
    }

    // Calculate how many items are visible
    function getVisibleItems() {
        const containerWidth = container.offsetWidth;
        const itemWidth = getScrollAmount();
        return Math.floor(containerWidth / itemWidth);
    }

    // Calculate maximum scroll position
    function getMaxScroll() {
        const containerWidth = container.offsetWidth;
        const totalWidth = vehicleTypeGrid.scrollWidth;
        return Math.max(0, totalWidth - containerWidth);
    }

    // Calculate total pages for mobile dots
    function getTotalPages() {
        const visibleItems = getVisibleItems();
        return Math.ceil(totalItems / visibleItems);
    }

    function updateNavigation() {
        const maxScroll = getMaxScroll();

        if (prevBtn && nextBtn) {
            // Update button states based on scroll position
            prevBtn.disabled = scrollAmount <= 0;
            nextBtn.disabled = scrollAmount >= maxScroll;

            // Add visual feedback
            if (prevBtn.disabled) {
                prevBtn.classList.add('opacity-50', 'cursor-not-allowed');
            } else {
                prevBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }

            if (nextBtn.disabled) {
                nextBtn.classList.add('opacity-50', 'cursor-not-allowed');
            } else {
                nextBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        }

        // Update mobile dots
        updateMobileDots();

        // Apply smooth transform
        vehicleTypeGrid.style.transform = `translateX(-${scrollAmount}px)`;
    }

    function updateMobileDots() {
        if (!mobileDots) return;

        const totalPages = getTotalPages();
        const visibleItems = getVisibleItems();
        const currentPage = Math.floor(currentIndex / visibleItems);

        mobileDots.innerHTML = '';

        // Only show dots if there are multiple pages
        if (totalPages > 1) {
            for (let i = 0; i < totalPages; i++) {
                const dot = document.createElement('button');
                dot.className = `w-2 h-2 rounded-full transition-all duration-300 ${
                    i === currentPage
                        ? 'bg-primary-red w-6'
                        : 'bg-gray-300 hover:bg-gray-400'
                }`;
                dot.addEventListener('click', () => goToPage(i));
                mobileDots.appendChild(dot);
            }
        }
    }

    function goToPage(page) {
        const visibleItems = getVisibleItems();
        const newIndex = page * visibleItems;
        const itemScrollAmount = getScrollAmount();

        currentIndex = Math.min(newIndex, totalItems - 1);
        scrollAmount = Math.min(currentIndex * itemScrollAmount, getMaxScroll());

        updateNavigation();
    }

    function goToPrevious() {
        const itemScrollAmount = getScrollAmount();
        const newScrollAmount = Math.max(0, scrollAmount - itemScrollAmount);

        if (newScrollAmount !== scrollAmount) {
            scrollAmount = newScrollAmount;
            currentIndex = Math.max(0, currentIndex - 1);
            updateNavigation();
        }
    }

    function goToNext() {
        const itemScrollAmount = getScrollAmount();
        const maxScroll = getMaxScroll();
        const newScrollAmount = Math.min(maxScroll, scrollAmount + itemScrollAmount);

        if (newScrollAmount !== scrollAmount) {
            scrollAmount = newScrollAmount;
            currentIndex = Math.min(totalItems - 1, currentIndex + 1);
            updateNavigation();
        }
    }

    // Event listeners
    if (prevBtn) {
        prevBtn.addEventListener('click', function(e) {
            e.preventDefault();
            goToPrevious();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', function(e) {
            e.preventDefault();
            goToNext();
        });
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        // Recalculate on resize
        scrollAmount = 0;
        currentIndex = 0;
        updateNavigation();
    });

    // Touch/swipe support for mobile
    let startX = 0;
    let startY = 0;
    let isScrolling = false;

    container.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isScrolling = false;
    }, { passive: true });

    container.addEventListener('touchmove', function(e) {
        if (!startX || !startY) return;

        const diffX = Math.abs(e.touches[0].clientX - startX);
        const diffY = Math.abs(e.touches[0].clientY - startY);

        // Determine if user is scrolling vertically
        if (diffY > diffX) {
            isScrolling = true;
        }
    }, { passive: true });

    container.addEventListener('touchend', function(e) {
        if (!startX || isScrolling) return;

        const endX = e.changedTouches[0].clientX;
        const diffX = startX - endX;
        const threshold = 50; // Minimum swipe distance

        if (Math.abs(diffX) > threshold) {
            if (diffX > 0) {
                // Swiped left - go to next
                goToNext();
            } else {
                // Swiped right - go to previous
                goToPrevious();
            }
        }

        startX = 0;
        startY = 0;
        isScrolling = false;
    }, { passive: true });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.target.closest('#vehicleTypeGrid')) {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                goToPrevious();
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                goToNext();
            }
        }
    });

    // Auto-scroll functionality (optional)
    let autoScrollInterval;

    function startAutoScroll() {
        autoScrollInterval = setInterval(function() {
            const maxScroll = getMaxScroll();
            if (scrollAmount >= maxScroll) {
                // Reset to beginning
                scrollAmount = 0;
                currentIndex = 0;
            } else {
                goToNext();
            }
        }, 4000); // Auto-scroll every 4 seconds
    }

    function stopAutoScroll() {
        if (autoScrollInterval) {
            clearInterval(autoScrollInterval);
        }
    }

    // Start auto-scroll
    startAutoScroll();

    // Pause auto-scroll on hover or interaction
    const section = container.closest('section');
    if (section) {
        section.addEventListener('mouseenter', stopAutoScroll);
        section.addEventListener('mouseleave', startAutoScroll);
    }

    // Pause auto-scroll on button interaction
    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            stopAutoScroll();
            setTimeout(startAutoScroll, 8000); // Resume after 8 seconds
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            stopAutoScroll();
            setTimeout(startAutoScroll, 8000); // Resume after 8 seconds
        });
    }

    // Initialize
    updateNavigation();
});

// Hot Deals and Promotion Features
document.addEventListener('DOMContentLoaded', function() {
    // Track featured car views
    document.querySelectorAll('#featured-cars .group').forEach(function(card) {
        card.addEventListener('click', function() {
            const carId = this.querySelector('a[href*="/cars/"]')?.href.match(/\/cars\/(\d+)\//)?.[1];
            if (carId) {
                // Track analytics
                fetch('/analytics/track/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || '',
                    },
                    body: JSON.stringify({
                        metric_type: 'featured_clicks',
                        car_id: carId
                    })
                }).catch(console.error);
            }
        });
    });

    // Auto-update hot deals every 30 seconds
    setInterval(function() {
        // Refresh hot deals section via HTMX if available
        if (typeof htmx !== 'undefined') {
            htmx.trigger('#hot-deals', 'refresh');
        }
    }, 30000);
});

// Enhanced Entrance Animations
function initEntranceAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('in-view');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all elements with entrance animation classes
    const animatedElements = document.querySelectorAll('.animate-entrance, .animate-slide-in-left, .animate-slide-in-right, .animate-scale-in');
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', initEntranceAnimations);

// Get Valuation Modal Functionality
document.addEventListener('DOMContentLoaded', function() {
    const getValuationBtn = document.getElementById('get-valuation-btn');
    const valuationModal = document.getElementById('valuation-modal');
    const closeModalBtn = document.getElementById('close-valuation-modal');
    const valuationForm = document.getElementById('valuation-form');

    // Open modal
    if (getValuationBtn) {
        getValuationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            valuationModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        });
    }

    // Close modal
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', function() {
            valuationModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        });
    }

    // Close modal when clicking outside
    valuationModal.addEventListener('click', function(e) {
        if (e.target === valuationModal) {
            valuationModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !valuationModal.classList.contains('hidden')) {
            valuationModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    });

    // Form submission (placeholder - would need backend implementation)
    if (valuationForm) {
        valuationForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(valuationForm);
            const make = formData.get('make');
            const model = formData.get('model');
            const year = formData.get('year');
            const mileage = formData.get('mileage');
            const condition = formData.get('condition');

            // Simple valuation calculation (placeholder)
            let baseValue = 1000000; // Base value in KSh
            const currentYear = new Date().getFullYear();
            const age = currentYear - parseInt(year);

            // Depreciation based on age
            baseValue -= (age * 50000);

            // Adjust for mileage
            const mileageValue = parseInt(mileage);
            if (mileageValue > 100000) {
                baseValue -= 200000;
            } else if (mileageValue > 50000) {
                baseValue -= 100000;
            }

            // Adjust for condition
            const conditionMultipliers = {
                'excellent': 1.2,
                'good': 1.0,
                'fair': 0.8,
                'poor': 0.6
            };

            baseValue *= conditionMultipliers[condition] || 1.0;

            // Ensure minimum value
            baseValue = Math.max(baseValue, 200000);

            // Display result
            const resultDiv = document.getElementById('valuation-result');
            resultDiv.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-xl p-4 mt-4">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <h4 class="font-bold text-green-800 font-montserrat">Estimated Value</h4>
                    </div>
                    <p class="text-2xl font-bold text-green-700 mt-2 font-montserrat">KSH ${baseValue.toLocaleString()}</p>
                    <p class="text-sm text-green-600 mt-1 font-raleway">This is an estimated value. Contact us for a detailed assessment.</p>
                </div>
            `;
        });
    }
});

// Test message popup functionality
function testMessagePopup() {
    console.log('Testing message popup...');
    console.log('User authenticated:', window.messageIntegration ? window.messageIntegration.isUserAuthenticated() : 'messageIntegration not found');

    if (window.messageIntegration) {
        console.log('Message integration found, checking for popup messages...');
        window.messageIntegration.checkForPopupMessages();
    } else {
        console.log('Message integration not found');
        // Try to manually fetch popup messages
        fetch('/messages/popup/', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        }).then(response => {
            console.log('Manual fetch response status:', response.status);
            return response.text();
        }).then(data => {
            console.log('Manual fetch response data:', data);
        }).catch(error => {
            console.error('Manual fetch error:', error);
        });
    }
}

// Add test button for debugging (remove in production)
document.addEventListener('DOMContentLoaded', function() {
    const testBtn = document.createElement('button');
    testBtn.textContent = 'Test Message Popup';
    testBtn.className = 'fixed bottom-4 right-4 bg-red-600 text-white px-4 py-2 rounded-lg z-50 shadow-lg hover:bg-red-700 transition-colors';
    testBtn.onclick = testMessagePopup;
    document.body.appendChild(testBtn);
});
</script>

<script src="{% static 'js/hot-deals.js' %}"></script>

{% endblock %}
